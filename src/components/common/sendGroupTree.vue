<template>
  <div class="dialog-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="false"
      :filter="true"
      :checkAll="false"
      :checkStrictly="true"
      :filter-source-data="filterSourceData"
      @checkbox-change="onCheckboxChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { onMounted, useTemplateRef } from 'vue'
  import { TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import { VxeTableEvents } from 'vxe-table'
  import { speakInfo } from '@/utils/speak'

  const tableTreeRef = useTemplateRef('tableTree')

  const onCheckboxChange: VxeTableEvents.CellDblclick<TreeNodeData> = row => {
    console.log('onCheckboxChange', row)
  }
  const filterSourceData = (row: TreeNodeData) => {
    if (row.nodeType !== TreeNodeType.Org) {
      row.parentOrgId = ''
      return false
    }
    return true
  }

  onMounted(() => {
    const listenGroupKeys = speakInfo.listenGroup
  })
</script>

<style lang="scss">
  .dialog-tree-wrapper {
    height: 100%;
    width: 100%;
    font-family: 'AlibabaPuHuiTi2';

    .vxe-table .vxe-table--render-wrapper .vxe-column-content {
      color: #1ac8ed;
    }
  }
</style>
